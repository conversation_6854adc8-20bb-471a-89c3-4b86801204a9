# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/calib3d.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/affine.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/async.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/base.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/bufferpool.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/check.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/cuda.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/cuda.inl.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/cuda_types.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/cv_cpu_dispatch.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/cv_cpu_helper.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/cvdef.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/cvstd.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/cvstd.inl.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/cvstd_wrapper.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/fast_math.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/hal/interface.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/hal/msa_macros.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/mat.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/mat.inl.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/matx.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/neon_utils.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/operations.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/optim.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/ovx.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/persistence.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/saturate.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/traits.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/types.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/utility.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/utils/instrumentation.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/utils/tls.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/version.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/vsx_utils.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/dnn.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/dnn/dict.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/dnn/dnn.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/dnn/dnn.inl.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/dnn/layer.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/dnn/utils/inference_engine.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/dnn/version.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/features2d.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/all_indices.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/allocator.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/any.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/autotuned_index.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/composite_index.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/config.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/defines.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/dist.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/dynamic_bitset.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/flann_base.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/general.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/ground_truth.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/heap.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/hierarchical_clustering_index.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/index_testing.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/kdtree_index.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/kdtree_single_index.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/kmeans_index.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/linear_index.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/logger.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/lsh_index.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/lsh_table.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/matrix.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/miniflann.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/nn_index.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/params.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/random.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/result_set.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/sampling.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/saving.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/timer.h
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/highgui.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/imgcodecs.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/imgproc.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/imgproc/segmentation.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/ml.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/ml/ml.inl.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/objdetect.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/objdetect/aruco_board.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/objdetect/aruco_detector.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/objdetect/aruco_dictionary.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/objdetect/barcode.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/objdetect/charuco_detector.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/objdetect/detection_based_tracker.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/objdetect/face.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/objdetect/graphical_code_detector.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/opencv.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/opencv_modules.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/photo.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching/detail/blenders.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching/detail/camera.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching/detail/exposure_compensate.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching/detail/matchers.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching/detail/motion_estimators.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching/detail/seam_finders.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching/detail/util.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching/detail/util_inl.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching/detail/warpers.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching/detail/warpers_inl.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching/warpers.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/video.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/video/background_segm.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/video/tracking.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/videoio.hpp
CMakeFiles/segment.dir/test_seg.cpp.o: ../test_seg.cpp
CMakeFiles/segment.dir/test_seg.cpp.o: ../yolov8_utils.h

CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/calib3d.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/affine.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/async.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/base.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/bufferpool.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/check.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/cuda.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/cuda.inl.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/cuda_types.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/cv_cpu_dispatch.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/cv_cpu_helper.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/cvdef.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/cvstd.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/cvstd.inl.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/cvstd_wrapper.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/fast_math.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/hal/interface.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/hal/msa_macros.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/mat.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/mat.inl.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/matx.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/neon_utils.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/operations.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/optim.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/ovx.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/persistence.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/saturate.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/traits.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/types.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/utility.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/utils/instrumentation.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/utils/tls.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/version.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/core/vsx_utils.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/dnn.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/dnn/dict.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/dnn/dnn.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/dnn/dnn.inl.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/dnn/layer.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/dnn/utils/inference_engine.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/dnn/version.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/features2d.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/all_indices.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/allocator.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/any.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/autotuned_index.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/composite_index.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/config.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/defines.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/dist.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/dynamic_bitset.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/flann_base.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/general.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/ground_truth.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/heap.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/hierarchical_clustering_index.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/index_testing.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/kdtree_index.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/kdtree_single_index.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/kmeans_index.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/linear_index.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/logger.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/lsh_index.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/lsh_table.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/matrix.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/miniflann.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/nn_index.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/params.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/random.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/result_set.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/sampling.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/saving.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/flann/timer.h
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/highgui.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/imgcodecs.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/imgproc.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/imgproc/segmentation.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/ml.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/ml/ml.inl.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/objdetect.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/objdetect/aruco_board.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/objdetect/aruco_detector.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/objdetect/aruco_dictionary.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/objdetect/barcode.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/objdetect/charuco_detector.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/objdetect/detection_based_tracker.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/objdetect/face.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/objdetect/graphical_code_detector.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/opencv.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/opencv_modules.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/photo.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching/detail/blenders.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching/detail/camera.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching/detail/exposure_compensate.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching/detail/matchers.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching/detail/motion_estimators.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching/detail/seam_finders.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching/detail/util.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching/detail/util_inl.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching/detail/warpers.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching/detail/warpers_inl.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/stitching/warpers.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/video.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/video/background_segm.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/video/tracking.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: /home/<USER>/Environments/opencv-4.8.0/release/include/opencv4/opencv2/videoio.hpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: ../yolov8_utils.cpp
CMakeFiles/segment.dir/yolov8_utils.cpp.o: ../yolov8_utils.h

