# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/segment.dir/all
all: CMakeFiles/classify.dir/all

.PHONY : all

# The main recursive "preinstall" target.
preinstall:

.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/segment.dir/clean
clean: CMakeFiles/classify.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/segment.dir

# All Build rule for target.
CMakeFiles/segment.dir/all:
	$(MAKE) -f CMakeFiles/segment.dir/build.make CMakeFiles/segment.dir/depend
	$(MAKE) -f CMakeFiles/segment.dir/build.make CMakeFiles/segment.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/build/CMakeFiles --progress-num=4,5,6 "Built target segment"
.PHONY : CMakeFiles/segment.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/segment.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/build/CMakeFiles 3
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/segment.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/build/CMakeFiles 0
.PHONY : CMakeFiles/segment.dir/rule

# Convenience name for target.
segment: CMakeFiles/segment.dir/rule

.PHONY : segment

# clean rule for target.
CMakeFiles/segment.dir/clean:
	$(MAKE) -f CMakeFiles/segment.dir/build.make CMakeFiles/segment.dir/clean
.PHONY : CMakeFiles/segment.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/classify.dir

# All Build rule for target.
CMakeFiles/classify.dir/all:
	$(MAKE) -f CMakeFiles/classify.dir/build.make CMakeFiles/classify.dir/depend
	$(MAKE) -f CMakeFiles/classify.dir/build.make CMakeFiles/classify.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/build/CMakeFiles --progress-num=1,2,3 "Built target classify"
.PHONY : CMakeFiles/classify.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/classify.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/build/CMakeFiles 3
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/classify.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/build/CMakeFiles 0
.PHONY : CMakeFiles/classify.dir/rule

# Convenience name for target.
classify: CMakeFiles/classify.dir/rule

.PHONY : classify

# clean rule for target.
CMakeFiles/classify.dir/clean:
	$(MAKE) -f CMakeFiles/classify.dir/build.make CMakeFiles/classify.dir/clean
.PHONY : CMakeFiles/classify.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

