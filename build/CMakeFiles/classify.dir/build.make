# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/build

# Include any dependencies generated for this target.
include CMakeFiles/classify.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/classify.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/classify.dir/flags.make

CMakeFiles/classify.dir/test_classify.cpp.o: CMakeFiles/classify.dir/flags.make
CMakeFiles/classify.dir/test_classify.cpp.o: ../test_classify.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/classify.dir/test_classify.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/classify.dir/test_classify.cpp.o -c /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/test_classify.cpp

CMakeFiles/classify.dir/test_classify.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/classify.dir/test_classify.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/test_classify.cpp > CMakeFiles/classify.dir/test_classify.cpp.i

CMakeFiles/classify.dir/test_classify.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/classify.dir/test_classify.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/test_classify.cpp -o CMakeFiles/classify.dir/test_classify.cpp.s

CMakeFiles/classify.dir/yolov8_utils.cpp.o: CMakeFiles/classify.dir/flags.make
CMakeFiles/classify.dir/yolov8_utils.cpp.o: ../yolov8_utils.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/classify.dir/yolov8_utils.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/classify.dir/yolov8_utils.cpp.o -c /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/yolov8_utils.cpp

CMakeFiles/classify.dir/yolov8_utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/classify.dir/yolov8_utils.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/yolov8_utils.cpp > CMakeFiles/classify.dir/yolov8_utils.cpp.i

CMakeFiles/classify.dir/yolov8_utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/classify.dir/yolov8_utils.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/yolov8_utils.cpp -o CMakeFiles/classify.dir/yolov8_utils.cpp.s

# Object files for target classify
classify_OBJECTS = \
"CMakeFiles/classify.dir/test_classify.cpp.o" \
"CMakeFiles/classify.dir/yolov8_utils.cpp.o"

# External object files for target classify
classify_EXTERNAL_OBJECTS =

classify: CMakeFiles/classify.dir/test_classify.cpp.o
classify: CMakeFiles/classify.dir/yolov8_utils.cpp.o
classify: CMakeFiles/classify.dir/build.make
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_gapi.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_stitching.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_aruco.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_bgsegm.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_bioinspired.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_ccalib.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_dnn_objdetect.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_dnn_superres.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_dpm.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_face.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_freetype.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_fuzzy.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_hfs.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_img_hash.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_intensity_transform.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_line_descriptor.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_mcc.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_quality.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_rapid.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_reg.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_rgbd.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_saliency.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_stereo.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_structured_light.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_superres.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_surface_matching.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_tracking.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_videostab.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_wechat_qrcode.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_xfeatures2d.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_xobjdetect.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_xphoto.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_shape.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_highgui.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_datasets.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_plot.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_text.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_ml.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_phase_unwrapping.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_optflow.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_ximgproc.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_video.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_videoio.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_imgcodecs.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_objdetect.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_calib3d.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_dnn.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_features2d.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_flann.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_photo.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_imgproc.so.4.8.0
classify: /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_core.so.4.8.0
classify: CMakeFiles/classify.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable classify"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/classify.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/classify.dir/build: classify

.PHONY : CMakeFiles/classify.dir/build

CMakeFiles/classify.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/classify.dir/cmake_clean.cmake
.PHONY : CMakeFiles/classify.dir/clean

CMakeFiles/classify.dir/depend:
	cd /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/build /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/build /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/build/CMakeFiles/classify.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/classify.dir/depend

