/usr/bin/c++    -rdynamic CMakeFiles/classify.dir/test_classify.cpp.o CMakeFiles/classify.dir/yolov8_utils.cpp.o  -o classify  -Wl,-rpath,/home/<USER>/Environments/opencv-4.8.0/release/lib /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_gapi.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_stitching.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_aruco.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_bgsegm.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_bioinspired.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_ccalib.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_dnn_objdetect.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_dnn_superres.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_dpm.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_face.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_freetype.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_fuzzy.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_hfs.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_img_hash.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_intensity_transform.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_line_descriptor.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_mcc.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_quality.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_rapid.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_reg.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_rgbd.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_saliency.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_stereo.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_structured_light.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_superres.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_surface_matching.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_tracking.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_videostab.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_wechat_qrcode.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_xfeatures2d.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_xobjdetect.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_xphoto.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_shape.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_highgui.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_datasets.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_plot.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_text.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_ml.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_phase_unwrapping.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_optflow.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_ximgproc.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_video.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_videoio.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_imgcodecs.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_objdetect.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_calib3d.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_dnn.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_features2d.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_flann.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_photo.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_imgproc.so.4.8.0 /home/<USER>/Environments/opencv-4.8.0/release/lib/libopencv_core.so.4.8.0 
