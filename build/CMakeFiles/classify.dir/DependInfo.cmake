# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "/home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/test_classify.cpp" "/home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/build/CMakeFiles/classify.dir/test_classify.cpp.o"
  "/home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/yolov8_utils.cpp" "/home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/build/CMakeFiles/classify.dir/yolov8_utils.cpp.o"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "/home/<USER>/Environments/opencv-4.8.0/release/include/opencv4"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
