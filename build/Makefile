# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	/home/<USER>/Environments/cmake-3.17.1-Linux-x86_64/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/build/CMakeFiles /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/build/CMakeFiles/progress.marks
	$(MAKE) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Projects/C++_Projects/yolov8/yolov8_opencv_onnx/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named segment

# Build rule for target.
segment: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 segment
.PHONY : segment

# fast build rule for target.
segment/fast:
	$(MAKE) -f CMakeFiles/segment.dir/build.make CMakeFiles/segment.dir/build
.PHONY : segment/fast

#=============================================================================
# Target rules for targets named classify

# Build rule for target.
classify: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 classify
.PHONY : classify

# fast build rule for target.
classify/fast:
	$(MAKE) -f CMakeFiles/classify.dir/build.make CMakeFiles/classify.dir/build
.PHONY : classify/fast

test_classify.o: test_classify.cpp.o

.PHONY : test_classify.o

# target to build an object file
test_classify.cpp.o:
	$(MAKE) -f CMakeFiles/classify.dir/build.make CMakeFiles/classify.dir/test_classify.cpp.o
.PHONY : test_classify.cpp.o

test_classify.i: test_classify.cpp.i

.PHONY : test_classify.i

# target to preprocess a source file
test_classify.cpp.i:
	$(MAKE) -f CMakeFiles/classify.dir/build.make CMakeFiles/classify.dir/test_classify.cpp.i
.PHONY : test_classify.cpp.i

test_classify.s: test_classify.cpp.s

.PHONY : test_classify.s

# target to generate assembly for a file
test_classify.cpp.s:
	$(MAKE) -f CMakeFiles/classify.dir/build.make CMakeFiles/classify.dir/test_classify.cpp.s
.PHONY : test_classify.cpp.s

test_seg.o: test_seg.cpp.o

.PHONY : test_seg.o

# target to build an object file
test_seg.cpp.o:
	$(MAKE) -f CMakeFiles/segment.dir/build.make CMakeFiles/segment.dir/test_seg.cpp.o
.PHONY : test_seg.cpp.o

test_seg.i: test_seg.cpp.i

.PHONY : test_seg.i

# target to preprocess a source file
test_seg.cpp.i:
	$(MAKE) -f CMakeFiles/segment.dir/build.make CMakeFiles/segment.dir/test_seg.cpp.i
.PHONY : test_seg.cpp.i

test_seg.s: test_seg.cpp.s

.PHONY : test_seg.s

# target to generate assembly for a file
test_seg.cpp.s:
	$(MAKE) -f CMakeFiles/segment.dir/build.make CMakeFiles/segment.dir/test_seg.cpp.s
.PHONY : test_seg.cpp.s

yolov8_utils.o: yolov8_utils.cpp.o

.PHONY : yolov8_utils.o

# target to build an object file
yolov8_utils.cpp.o:
	$(MAKE) -f CMakeFiles/segment.dir/build.make CMakeFiles/segment.dir/yolov8_utils.cpp.o
	$(MAKE) -f CMakeFiles/classify.dir/build.make CMakeFiles/classify.dir/yolov8_utils.cpp.o
.PHONY : yolov8_utils.cpp.o

yolov8_utils.i: yolov8_utils.cpp.i

.PHONY : yolov8_utils.i

# target to preprocess a source file
yolov8_utils.cpp.i:
	$(MAKE) -f CMakeFiles/segment.dir/build.make CMakeFiles/segment.dir/yolov8_utils.cpp.i
	$(MAKE) -f CMakeFiles/classify.dir/build.make CMakeFiles/classify.dir/yolov8_utils.cpp.i
.PHONY : yolov8_utils.cpp.i

yolov8_utils.s: yolov8_utils.cpp.s

.PHONY : yolov8_utils.s

# target to generate assembly for a file
yolov8_utils.cpp.s:
	$(MAKE) -f CMakeFiles/segment.dir/build.make CMakeFiles/segment.dir/yolov8_utils.cpp.s
	$(MAKE) -f CMakeFiles/classify.dir/build.make CMakeFiles/classify.dir/yolov8_utils.cpp.s
.PHONY : yolov8_utils.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... segment"
	@echo "... classify"
	@echo "... test_classify.o"
	@echo "... test_classify.i"
	@echo "... test_classify.s"
	@echo "... test_seg.o"
	@echo "... test_seg.i"
	@echo "... test_seg.s"
	@echo "... yolov8_utils.o"
	@echo "... yolov8_utils.i"
	@echo "... yolov8_utils.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

