#pragma once
#include <fstream>
#include <vector>
#include <string>
#include <random>

#include <math.h>
#include <time.h>

// OpenCV / DNN / Inference
#include <opencv2/imgproc.hpp>
#include <opencv2/opencv.hpp>
#include <opencv2/dnn.hpp>

class YOLOSegment
{
public:
    YOLOSegment();
    virtual ~YOLOSegment() = default;
    void infer(const cv::Mat &input, std::vector<OutputSeg> &output);


private:
    void loadSegFromFile();
    void loadOnnxNetwork();
    cv::Mat formatToSquare(const cv::Mat &source);

    void letterBox(const cv::Mat &image, cv::Mat &outImage,
                   cv::Vec4d &params, //[ratio_x,ratio_y,dw,dh]
                   const cv::Size &newShape = cv::Size(640, 640),
                   bool autoShape = false,
                   bool scaleFill = false,
                   bool scaleUp = true,
                   int stride = 32,
                   const cv::Scalar &color = cv::Scalar(114, 114, 114));

    std::string modelPath{};
    std::string classesPath{};

    cv::Size2f modelShape{640, 640};

    float _classThreshold = 0.25;
    float _nmsThreshold = 0.45;
    float _maskThreshold = 0.5;

    const int _netWidth = 640;  // ONNX图片输入宽度
    const int _netHeight = 640; // ONNX图片输入高度

    int _segWidth = 160; //_segWidth=_netWidth/mask_ratio
    int _segHeight = 160;
    int _segChannels = 32;

    cv::Vec4d params;

    bool letterBoxForSquare = true;
    bool cudaEnabled = false;

    cv::dnn::Net net;
};