#pragma once
#include "ultralytics/base/yolo_base.h"
#include <vector>
#include <string>
#include <random>
#include <filesystem>
#include <algorithm> 

#include <math.h>
#include <time.h>

#include "yolov8_utils.h"

// OpenCV / DNN / Inference
#include <opencv2/imgproc.hpp>
#include <opencv2/opencv.hpp>
#include <opencv2/dnn.hpp>


class YOLOClassifier : public YOLOBase
{
public:
    YOLOClassifier(const std::string &onnxModelPath, const std::string &classesTxtFile, bool cudaEnabled = false);
    virtual ~YOLOClassifier() = default;
    void infer(const cv::Mat &input, std::vector<outputClassify> &output);

private:
    void loadClassesFromFile();
    void loadOnnxNetwork();
    cv::Mat formatToSquare(const cv::Mat &source);

    std::vector<std::string> classes;

    cv::Size2f modelShape{224, 224};

    std::string modelPath{};
    std::string classesPath{};

    bool cudaEnabled = false;
    bool letterBoxForSquare = true;

    cv::dnn::Net net;
};