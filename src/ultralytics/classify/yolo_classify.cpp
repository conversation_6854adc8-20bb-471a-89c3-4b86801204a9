#include "ultralytics/classify/yolo_classify.h"

YOLOClassifier::YOLOClassifier(const std::string &onnxModelPath, const std::string &classesTxtFile, bool cudaEnabled)
{
    this->modelPath = onnxModelPath;
    this->classesPath = classesTxtFile;
    this->cudaEnabled = cudaEnabled;

    loadOnnxNetwork();
    loadClassesFromFile(); // The classes are hard-coded for this example)
}

void YOLOClassifier::loadClassesFromFile()
{
    std::ifstream inputFile(this->classesPath);
    if (inputFile.is_open())
    {
        std::string classLine;
        while (std::getline(inputFile, classLine))
            classes.push_back(classLine);
        inputFile.close();
    }
}

void YOLOClassifier::loadOnnxNetwork()
{
    net = cv::dnn::readNetFromONNX(this->modelPath);
    if (this->cudaEnabled)
    {
        std::cout << "\nRunning on CUDA" << std::endl;
        net.setPreferableBackend(cv::dnn::DNN_BACKEND_CUDA);
        net.setPreferableTarget(cv::dnn::DNN_TARGET_CUDA);
    }
    else
    {
        std::cout << "\nRunning on CPU" << std::endl;
        net.setPreferableBackend(cv::dnn::DNN_BACKEND_OPENCV);
        net.setPreferableTarget(cv::dnn::DNN_TARGET_CPU);
    }
}

cv::Mat YOLOClassifier::formatToSquare(const cv::Mat &source)
{
    int col = source.cols;
    int row = source.rows;


    int _min = MIN(col, row);
    // 计算中心裁剪区域
    int top = (row - _min) / 2;
    int left = (col - _min) / 2;
    cv::Mat cropped = source(cv::Rect(left, top, _min, _min));
    cv::Mat resized;
    cv::resize(cropped, resized, cv::Size(224, 224), 0, 0, cv::INTER_LINEAR);
    return resized;
}

std::vector<std::pair<std::string, float>> YOLOClassifier::infer(const cv::Mat &input)
{

}