#include "ultralytics/classify/yolo_classify.h"

YOLOClassifier::YOLOClassifier(const std::string &onnxModelPath, const std::string &classesTxtFile, bool cudaEnabled)
{
    this->modelPath = onnxModelPath;
    this->classesPath = classesTxtFile;
    this->cudaEnabled = cudaEnabled;

    loadOnnxNetwork();
    loadClassesFromFile(); // The classes are hard-coded for this example)
}

void YOLOClassifier::loadClassesFromFile()
{
    std::ifstream inputFile(this->classesPath);
    if (inputFile.is_open())
    {
        std::string classLine;
        while (std::getline(inputFile, classLine))
            classes.push_back(classLine);
        inputFile.close();
    }
}

void YOLOClassifier::loadOnnxNetwork()
{
    net = cv::dnn::readNetFromONNX(this->modelPath);
    if (this->cudaEnabled)
    {
        std::cout << "\nRunning on CUDA" << std::endl;
        net.setPreferableBackend(cv::dnn::DNN_BACKEND_CUDA);
        net.setPreferableTarget(cv::dnn::DNN_TARGET_CUDA);
    }
    else
    {
        std::cout << "\nRunning on CPU" << std::endl;
        net.setPreferableBackend(cv::dnn::DNN_BACKEND_OPENCV);
        net.setPreferableTarget(cv::dnn::DNN_TARGET_CPU);
    }
}

cv::Mat YOLOClassifier::formatToSquare(const cv::Mat &source)
{
    int col = source.cols;
    int row = source.rows;


    int _min = MIN(col, row);
    // 计算中心裁剪区域
    int top = (row - _min) / 2;
    int left = (col - _min) / 2;
    cv::Mat cropped = source(cv::Rect(left, top, _min, _min));
    cv::Mat resized;
    cv::resize(cropped, resized, cv::Size(224, 224), 0, 0, cv::INTER_LINEAR);
    return resized;
}

void YOLOClassifier::infer(const cv::Mat &input, std::vector<outputClassify> &output)
{
    std::cout << "runYolov8" << std::endl;
    cv::Mat modelInput = input;
    if (letterBoxForSquare && modelShape.width == modelShape.height)
        modelInput = formatToSquare(modelInput); // 1280*1280
    std::cout << "letterbox end" << std::endl;

    cv::Mat blob;
    cv::dnn::blobFromImage(modelInput, blob, 1.0 / 255.0, modelShape, cv::Scalar(), true, false);

    std::cout << "blobFromImage end" << std::endl;

    net.setInput(blob);
    std::vector<cv::Mat> outputs;
    net.forward(outputs, net.getUnconnectedOutLayersNames());
    std::cout << "forward end" << std::endl;

    std::cout << "outputs size :" << outputs[0].size() << std::endl;

    // 3. 获取输出张量（shape: [1, num_classes]）
    cv::Mat &prob = outputs[0];
    prob = prob.reshape(1, 1); // 确保形状是 [1, N]

    // 4. 取 Top-5
    std::vector<std::pair<float, int>> prob_index;
    for (int i = 0; i < prob.cols; i++)
    {
        prob_index.emplace_back(prob.at<float>(0, i), i);
    }

    // 按概率降序排序
    std::sort(prob_index.begin(), prob_index.end(),
              [](const std::pair<float, int> &a, const std::pair<float, int> &b)
              {
                  return a.first > b.first;
              });

    // 5. 打印 Top-5 结果
    std::cout << "Top-5 predictions:\n";

    for (int i = 0; i < 5 && i < prob_index.size(); i++)
    {
        outputClassify result;
        int label_id = prob_index[i].second;
        float confidence = prob_index[i].first;
        std::string label = (label_id < classes.size()) ? classes[label_id] : std::to_string(label_id);
        std::cout << "  Rank-" << i + 1
            << ": label=" << label_id
            << " (" << label << ")"
            << ", prob=" << confidence
            << "\n";
        result.label = label;
        result.confidence = confidence;
        output.push_back(result);
    }
}