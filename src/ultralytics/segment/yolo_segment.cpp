#include "ultralytics/segment/yolo_segment.h"

YOLOSegment::YOLOSegment(const std::string &onnxModelPath, const std::string &classesTxtFile, bool cudaEnabled)
{
    this->modelPath = onnxModelPath;
    this->classesPath = classesTxtFile;
    this->cudaEnabled = cudaEnabled;

    loadOnnxNetwork();
    loadSegFromFile(); // The classes are hard-coded for this example
}

void YOLOSegment::loadOnnxNetwork()
{
    net = cv::dnn::readNetFromONNX(this->modelPath);
    if (this->cudaEnabled)
    {
        std::cout << "\nRunning on CUDA" << std::endl;
        net.setPreferableBackend(cv::dnn::DNN_BACKEND_CUDA);
        net.setPreferableTarget(cv::dnn::DNN_TARGET_CUDA);
        // net.setPreferableTarget(cv::dnn::DNN_TARGET_CUDA_FP16);
    }
    else
    {
        std::cout << "\nRunning on CPU" << std::endl;
        net.setPreferableBackend(cv::dnn::DNN_BACKEND_OPENCV);
        net.setPreferableTarget(cv::dnn::DNN_TARGET_CPU);
    }
}

void YOLOSegment::loadSegFromFile()
{
    std::ifstream inputFile(this->classesPath);
    if (inputFile.is_open())
    {
        std::string classLine;
        while (std::getline(inputFile, classLine))
            classes.push_back(classLine);
        inputFile.close();
    }
}

cv::Mat YOLOSegment::formatToSquare(const cv::Mat &source)
{
    int col = source.cols;
    int row = source.rows;
    int _max = MAX(col, row);
    cv::Mat result = cv::Mat::zeros(_max, _max, CV_8UC3);
    source.copyTo(result(cv::Rect(0, 0, col, row)));
    return result;
}