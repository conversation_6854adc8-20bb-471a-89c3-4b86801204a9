#include "ultralytics/segment/yolo_segment.h"

YOLOSegment::YOLOSegment(const std::string &onnxModelPath, const std::string &classesTxtFile, bool cudaEnabled)
{
    this->modelPath = onnxModelPath;
    this->classesPath = classesTxtFile;
    this->cudaEnabled = cudaEnabled;

    loadOnnxNetwork();
    loadSegFromFile(); // The classes are hard-coded for this example
}

void YOLOSegment::loadOnnxNetwork()
{
    net = cv::dnn::readNetFromONNX(this->modelPath);
    if (this->cudaEnabled)
    {
        std::cout << "\nRunning on CUDA" << std::endl;
        net.setPreferableBackend(cv::dnn::DNN_BACKEND_CUDA);
        net.setPreferableTarget(cv::dnn::DNN_TARGET_CUDA);
        // net.setPreferableTarget(cv::dnn::DNN_TARGET_CUDA_FP16);
    }
    else
    {
        std::cout << "\nRunning on CPU" << std::endl;
        net.setPreferableBackend(cv::dnn::DNN_BACKEND_OPENCV);
        net.setPreferableTarget(cv::dnn::DNN_TARGET_CPU);
    }
}

void YOLOSegment::loadSegFromFile()
{
    std::ifstream inputFile(this->classesPath);
    if (inputFile.is_open())
    {
        std::string classLine;
        while (std::getline(inputFile, classLine))
            classes.push_back(classLine);
        inputFile.close();
    }
}

cv::Mat YOLOSegment::formatToSquare(const cv::Mat &source)
{
    int col = source.cols;
    int row = source.rows;
    int _max = MAX(col, row);
    cv::Mat result = cv::Mat::zeros(_max, _max, CV_8UC3);
    source.copyTo(result(cv::Rect(0, 0, col, row)));
    return result;
}

void YOLOSegment::infer(const cv::Mat &input, std::vector<OutputSeg> &output)
{
cv::Mat modelInput = input;
    cv::Mat blob;

    int col = modelInput.cols;
    int row = modelInput.rows;
    cv::Mat netInputImg;
    cv::Vec4d params;
    LetterBox(modelInput, netInputImg, params, cv::Size(_netWidth, _netHeight));

    printf("netInputImg info is  width : %d, height : %d\n", netInputImg.cols, netInputImg.rows);

    cv::dnn::blobFromImage(netInputImg, blob, 1.0 / 255.0, modelShape, cv::Scalar(), true, false);

    net.setInput(blob);
    std::vector<cv::Mat> outputs;
    std::vector<std::string> output_layer_names{"output0", "output1"};
    net.forward(outputs, output_layer_names);

    // outputs size is 2; outputs[0] info [1, 116, 8400], outputs[1] info [1, 32, 160, 160]
    printf("outputs[0] info is  size[1]:%d, size[2]:%d\n", outputs[0].size[1], outputs[0].size[2]);
    printf("outputs[1] info is  size[1]:%d, size[2]:%d, size[3]:%d\n", outputs[1].size[1], outputs[1].size[2], outputs[1].size[3]);

    std::vector<int> class_ids;                        // res-class_id
    std::vector<float> confidences;                    // res-conf
    std::vector<cv::Rect> boxes;                       // res-box
    std::vector<std::vector<float>> picked_proposals;  // output0[:,:, 4 + _className.size():net_width]===> for mask
    int net_width = classes.size() + 4 + _segChannels; // yolov8 : net_width = 116, 目标框中心点坐标4个数值x,y,w,h 以及80类分类值，32为掩膜置信度

    cv::Mat output0 = cv::Mat(cv::Size(outputs[0].size[2], outputs[0].size[1]), CV_32F, outputs[0].ptr<float>()).t();
    int rows = output0.rows;
    printf("output0 info is  cols:%d, rows:%d\n", output0.cols, output0.rows);

    float *pdata = output0.ptr<float>();

    for (int i = 0; i < rows; ++i)
    {

        cv::Mat scores(1, classes.size(), CV_32FC1, pdata + 4);
        cv::Point classIdPoint;
        double max_class_socre;
        cv::minMaxLoc(scores, 0, &max_class_socre, 0, &classIdPoint);
        max_class_socre = (float)max_class_socre;

        if (max_class_socre >= _classThreshold)
        {

            std::vector<float> temp_proto(pdata + 4 + classes.size(), pdata + net_width); // 后32位掩膜
            picked_proposals.push_back(temp_proto);

            // rect
            float x = (pdata[0] - params[2]) / params[0];
            float y = (pdata[1] - params[3]) / params[1];
            float w = pdata[2] / params[0];
            float h = pdata[3] / params[1];
            int left = MAX(int(x - 0.5 * w + 0.5), 0);
            int top = MAX(int(y - 0.5 * h + 0.5), 0);

            class_ids.push_back(classIdPoint.x);
            confidences.push_back(max_class_socre);
            boxes.push_back(cv::Rect(left, top, int(w + 0.5), int(h + 0.5)));
        }

        pdata += net_width;
    }

    printf("nms start...");
    std::vector<int> nms_result;
    cv::dnn::NMSBoxes(boxes, confidences, _classThreshold, _nmsThreshold, nms_result);
    std::vector<std::vector<float>> temp_mask_proposals;
    cv::Rect holeImgRect(0, 0, modelInput.cols, modelInput.rows);

    for (int i = 0; i < nms_result.size(); i++)
    {
        int idx = nms_result[i];

        OutputSeg result;
        result.id = class_ids[idx];
        result.confidence = confidences[idx];
        result.box = boxes[idx] & holeImgRect;

        temp_mask_proposals.push_back(picked_proposals[idx]);
        output.push_back(result);
    }

    MaskParams mask_params;
    mask_params.params = params;
    mask_params.srcImgShape = modelInput.size();
    for (int i = 0; i < temp_mask_proposals.size(); ++i)
    {
        GetMask2(cv::Mat(temp_mask_proposals[i]).t(), outputs[1], output[i], mask_params);
    }

    //******************** ****************
    // 老版本的方案，如果上面在开启我注释的部分之后还一直报错，建议使用这个。
    // If the GetMask2() still reports errors , it is recommended to use GetMask().
    // cv::Mat mask_proposals;
    // for (int i = 0; i < temp_mask_proposals.size(); ++i)
    //     mask_proposals.push_back(cv::Mat(temp_mask_proposals[i]).t());
    // printf("get mask start...");
    // GetMask(mask_proposals, outputs[1], output, mask_params);
    //*****************************************************/
}