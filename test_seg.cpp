#include <fstream>
#include <vector>
#include <string>
#include <random>

#include "yolov8_utils.h"
#include <math.h>
#include <time.h>

// OpenCV / DNN / Inference
#include <opencv2/imgproc.hpp>
#include <opencv2/opencv.hpp>
#include <opencv2/dnn.hpp>

class Yolov8
{
public:
    Yolov8(const std::string &onnxModelPath, const std::string &classesTxtFile = "");
    void runYolov8(const cv::Mat &input, std::vector<OutputSeg> &output);
    void drawPred(cv::Mat &img, std::vector<OutputSeg> result, std::vector<std::string> classNames, std::vector<cv::Scalar> color);
    std::vector<std::string> classes;

private:
    void loadSegFromFile();
    void loadOnnxNetwork();
    cv::Mat formatToSquare(const cv::Mat &source);

    void letterBox(const cv::Mat &image, cv::Mat &outImage,
                   cv::Vec4d &params, //[ratio_x,ratio_y,dw,dh]
                   const cv::Size &newShape = cv::Size(640, 640),
                   bool autoShape = false,
                   bool scaleFill = false,
                   bool scaleUp = true,
                   int stride = 32,
                   const cv::Scalar &color = cv::Scalar(114, 114, 114));

    std::string modelPath{};
    std::string classesPath{};

    cv::Size2f modelShape{640, 640};
    bool cudaEnabled = false;

    float _classThreshold = 0.25;
    float _nmsThreshold = 0.45;
    float _maskThreshold = 0.5;

    const int _netWidth = 640;  // ONNX图片输入宽度
    const int _netHeight = 640; // ONNX图片输入高度

    int _segWidth = 160; //_segWidth=_netWidth/mask_ratio
    int _segHeight = 160;
    int _segChannels = 32;

    cv::Vec4d params;

    bool letterBoxForSquare = true;

    cv::dnn::Net net;
};

Yolov8::Yolov8(const std::string &onnxModelPath, const std::string &classesTxtFile)
{
    modelPath = onnxModelPath;

    classesPath = classesTxtFile;

    loadOnnxNetwork();
    loadSegFromFile(); // The classes are hard-coded for this example
}

void Yolov8::letterBox(const cv::Mat &image, cv::Mat &outImage, cv::Vec4d &params, const cv::Size &newShape,
                       bool autoShape, bool scaleFill, bool scaleUp, int stride, const cv::Scalar &color)
{
    if (false)
    {
        int maxLen = MAX(image.rows, image.cols);
        outImage = cv::Mat::zeros(cv::Size(maxLen, maxLen), CV_8UC3);
        image.copyTo(outImage(cv::Rect(0, 0, image.cols, image.rows)));
        params[0] = 1;
        params[1] = 1;
        params[3] = 0;
        params[2] = 0;
    }

    cv::Size shape = image.size();
    float r = std::min((float)newShape.height / (float)shape.height,
                       (float)newShape.width / (float)shape.width);
    if (!scaleUp)
        r = std::min(r, 1.0f);

    float ratio[2]{r, r};
    int new_un_pad[2] = {(int)std::round((float)shape.width * r), (int)std::round((float)shape.height * r)};

    auto dw = (float)(newShape.width - new_un_pad[0]);
    auto dh = (float)(newShape.height - new_un_pad[1]);

    if (autoShape)
    {
        dw = (float)((int)dw % stride);
        dh = (float)((int)dh % stride);
    }
    else if (scaleFill)
    {
        dw = 0.0f;
        dh = 0.0f;
        new_un_pad[0] = newShape.width;
        new_un_pad[1] = newShape.height;
        ratio[0] = (float)newShape.width / (float)shape.width;
        ratio[1] = (float)newShape.height / (float)shape.height;
    }

    dw /= 2.0f;
    dh /= 2.0f;

    if (shape.width != new_un_pad[0] && shape.height != new_un_pad[1])
    {
        cv::resize(image, outImage, cv::Size(new_un_pad[0], new_un_pad[1]));
    }
    else
    {
        outImage = image.clone();
    }

    int top = int(std::round(dh - 0.1f));
    int bottom = int(std::round(dh + 0.1f));
    int left = int(std::round(dw - 0.1f));
    int right = int(std::round(dw + 0.1f));
    params[0] = ratio[0];
    params[1] = ratio[1];
    params[2] = left;
    params[3] = top;
    cv::copyMakeBorder(outImage, outImage, top, bottom, left, right, cv::BORDER_CONSTANT, color);
}

void Yolov8::runYolov8(const cv::Mat &input, std::vector<OutputSeg> &output)
{
    cv::Mat modelInput = input;
    cv::Mat blob;

    int col = modelInput.cols;
    int row = modelInput.rows;
    cv::Mat netInputImg;
    cv::Vec4d params;
    LetterBox(modelInput, netInputImg, params, cv::Size(_netWidth, _netHeight));

    printf("netInputImg info is  width : %d, height : %d\n", netInputImg.cols, netInputImg.rows);

    cv::dnn::blobFromImage(netInputImg, blob, 1.0 / 255.0, modelShape, cv::Scalar(), true, false);

    net.setInput(blob);
    std::vector<cv::Mat> outputs;
    std::vector<std::string> output_layer_names{"output0", "output1"};
    net.forward(outputs, output_layer_names);

    // outputs size is 2; outputs[0] info [1, 116, 8400], outputs[1] info [1, 32, 160, 160]
    printf("outputs[0] info is  size[1]:%d, size[2]:%d\n", outputs[0].size[1], outputs[0].size[2]);
    printf("outputs[1] info is  size[1]:%d, size[2]:%d, size[3]:%d\n", outputs[1].size[1], outputs[1].size[2], outputs[1].size[3]);

    std::vector<int> class_ids;                        // res-class_id
    std::vector<float> confidences;                    // res-conf
    std::vector<cv::Rect> boxes;                       // res-box
    std::vector<std::vector<float>> picked_proposals;  // output0[:,:, 4 + _className.size():net_width]===> for mask
    int net_width = classes.size() + 4 + _segChannels; // yolov8 : net_width = 116, 目标框中心点坐标4个数值x,y,w,h 以及80类分类值，32为掩膜置信度

    cv::Mat output0 = cv::Mat(cv::Size(outputs[0].size[2], outputs[0].size[1]), CV_32F, outputs[0].ptr<float>()).t();
    int rows = output0.rows;
    printf("output0 info is  cols:%d, rows:%d\n", output0.cols, output0.rows);

    float *pdata = output0.ptr<float>();

    for (int i = 0; i < rows; ++i)
    {

        cv::Mat scores(1, classes.size(), CV_32FC1, pdata + 4);
        cv::Point classIdPoint;
        double max_class_socre;
        cv::minMaxLoc(scores, 0, &max_class_socre, 0, &classIdPoint);
        max_class_socre = (float)max_class_socre;

        if (max_class_socre >= _classThreshold)
        {

            std::vector<float> temp_proto(pdata + 4 + classes.size(), pdata + net_width); // 后32位掩膜
            picked_proposals.push_back(temp_proto);

            // rect
            float x = (pdata[0] - params[2]) / params[0];
            float y = (pdata[1] - params[3]) / params[1];
            float w = pdata[2] / params[0];
            float h = pdata[3] / params[1];
            int left = MAX(int(x - 0.5 * w + 0.5), 0);
            int top = MAX(int(y - 0.5 * h + 0.5), 0);

            class_ids.push_back(classIdPoint.x);
            confidences.push_back(max_class_socre);
            boxes.push_back(cv::Rect(left, top, int(w + 0.5), int(h + 0.5)));
        }

        pdata += net_width;
    }

    printf("nms start...");
    std::vector<int> nms_result;
    cv::dnn::NMSBoxes(boxes, confidences, _classThreshold, _nmsThreshold, nms_result);
    std::vector<std::vector<float>> temp_mask_proposals;
    cv::Rect holeImgRect(0, 0, modelInput.cols, modelInput.rows);

    for (int i = 0; i < nms_result.size(); i++)
    {
        int idx = nms_result[i];

        OutputSeg result;
        result.id = class_ids[idx];
        result.confidence = confidences[idx];
        result.box = boxes[idx] & holeImgRect;

        temp_mask_proposals.push_back(picked_proposals[idx]);
        output.push_back(result);
    }

    MaskParams mask_params;
    mask_params.params = params;
    mask_params.srcImgShape = modelInput.size();
    for (int i = 0; i < temp_mask_proposals.size(); ++i)
    {
        GetMask2(cv::Mat(temp_mask_proposals[i]).t(), outputs[1], output[i], mask_params);
    }

    //******************** ****************
    // 老版本的方案，如果上面在开启我注释的部分之后还一直报错，建议使用这个。
    // If the GetMask2() still reports errors , it is recommended to use GetMask().
    // cv::Mat mask_proposals;
    // for (int i = 0; i < temp_mask_proposals.size(); ++i)
    //     mask_proposals.push_back(cv::Mat(temp_mask_proposals[i]).t());
    // printf("get mask start...");
    // GetMask(mask_proposals, outputs[1], output, mask_params);
    //*****************************************************/
}

void Yolov8::drawPred(cv::Mat &img, std::vector<OutputSeg> result, std::vector<std::string> classNames, std::vector<cv::Scalar> color)
{
    cv::Mat mask = img.clone();
    for (int i = 0; i < result.size(); i++)
    {
        int left, top;
        left = result[i].box.x;
        top = result[i].box.y;
        int color_num = i;
        // rectangle(img, result[i].box, color[result[i].id], 2, 8);
        cv::rectangle(img, result[i].box, color[result[i].id], 2, 8);
        if (result[i].boxMask.rows && result[i].boxMask.cols > 0)
            mask(result[i].box).setTo(color[result[i].id], result[i].boxMask);
        std::string label = classNames[result[i].id] + ":" + std::to_string(result[i].confidence);
        int baseLine;
        cv::Size labelSize = cv::getTextSize(label, cv::FONT_HERSHEY_SIMPLEX, 0.5, 1, &baseLine);
        top = std::max(top, labelSize.height);
        // rectangle(frame, Point(left, top - int(1.5 * labelSize.height)), Point(left + int(1.5 * labelSize.width), top + baseLine), Scalar(0, 255, 0), FILLED);
        cv::putText(img, label, cv::Point(left, top), cv::FONT_HERSHEY_SIMPLEX, 1, color[result[i].id], 2);
    }
    cv::addWeighted(img, 0.5, mask, 0.5, 0, img); // add mask to src
    cv::imshow("1", img);
    // imwrite("out.bmp", img);
    cv::waitKey(0);
    // destroyAllWindows();
}

void Yolov8::loadSegFromFile()
{
    std::ifstream inputFile(classesPath);
    if (inputFile.is_open())
    {
        std::string classLine;
        while (std::getline(inputFile, classLine))
            classes.push_back(classLine);
        inputFile.close();
    }
}

void Yolov8::loadOnnxNetwork()
{
    net = cv::dnn::readNetFromONNX(modelPath);
    if (cudaEnabled)
    {
        std::cout << "\nRunning on CUDA" << std::endl;
        net.setPreferableBackend(cv::dnn::DNN_BACKEND_CUDA);
        net.setPreferableTarget(cv::dnn::DNN_TARGET_CUDA);
        // net.setPreferableTarget(cv::dnn::DNN_TARGET_CUDA_FP16);
    }
    else
    {
        std::cout << "\nRunning on CPU" << std::endl;
        net.setPreferableBackend(cv::dnn::DNN_BACKEND_OPENCV);
        net.setPreferableTarget(cv::dnn::DNN_TARGET_CPU);
    }
}

cv::Mat Yolov8::formatToSquare(const cv::Mat &source)
{
    int col = source.cols;
    int row = source.rows;
    int _max = MAX(col, row);
    cv::Mat result = cv::Mat::zeros(_max, _max, CV_8UC3);
    source.copyTo(result(cv::Rect(0, 0, col, row)));
    return result;
}

int main(int argc, char **argv)
{
    // std::string img_path = "../data/test.jpg";
    // std::string img_path = "../data/watermelon_18.jpg";
    // std::string img_path = "../data/broccoli_112.jpg";
    std::string img_path = "/data/dukto/pictures/人体/1.jpg";
    std::string segment_model_path = "/data/DataBackup/projects/Python_Projects/yolov8/ultralytics-main/weights/yolov8n-seg.onnx";
    std::string segment_label_path = "/data/DataBackup/projects/Python_Projects/yolov8/ultralytics-main/weights/coco.names";

    std::vector<cv::Scalar> color;
    srand(time(0));
    for (int i = 0; i < 80; i++)
    {
        int b = rand() % 256;
        int g = rand() % 256;
        int r = rand() % 256;
        color.push_back(cv::Scalar(b, g, r));
    }
    std::vector<OutputSeg> result;

    cv::Mat img = cv::imread(img_path);
    cv::Mat imgInput = img.clone();

    while (1)
    {
        auto start_cap_time = std::chrono::system_clock::now(); // 当前时间

        Yolov8 yolov8(segment_model_path, segment_label_path);
        yolov8.runYolov8(imgInput, result);
        // yolov8.drawPred(imgInput, result, yolov8.classes, color);
        auto cap_time_now5 = std::chrono::system_clock::now(); // 当前时间

        int duration_cap_ = (int)std::chrono::duration_cast<std::chrono::milliseconds>(cap_time_now5 - start_cap_time).count();
        std::cout << "capture duration :" << duration_cap_ << " ms" << std::endl;
    }
    return 0;
}