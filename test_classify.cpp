#include <fstream>
#include <vector>
#include <string>
#include <random>

#include "yolov8_utils.h"
#include <math.h>
#include <time.h>

// OpenCV / DNN / Inference
#include <opencv2/imgproc.hpp>
#include <opencv2/opencv.hpp>
#include <opencv2/dnn.hpp>

#include <filesystem>
namespace fs = std::filesystem;

class Yolov8
{
public:
    Yolov8(const std::string &onnxModelPath, const std::string &classesTxtFile = "");
    std::vector<std::pair<std::string, float>> runYolov8(const cv::Mat &input);

private:
    void loadClassesFromFile();
    void loadOnnxNetwork();
    cv::Mat formatToSquare(const cv::Mat &source);

    std::string modelPath{};
    std::string classesPath{};

    std::vector<std::string> classes;

    cv::Size2f modelShape{224, 224};
    bool cudaEnabled = false;
    // float modelConfidenceThreshold {0.25};
    // float modelScoreThreshold      {0.45};
    // float modelNMSThreshold        {0.7};

    bool letterBoxForSquare = true;

    cv::dnn::Net net;
};

Yolov8::Yolov8(const std::string &onnxModelPath, const std::string &classesTxtFile)
{
    modelPath = onnxModelPath;

    classesPath = classesTxtFile;

    loadOnnxNetwork();
    loadClassesFromFile(); // The classes are hard-coded for this example
}

std::vector<std::pair<std::string, float>> Yolov8::runYolov8(const cv::Mat &input)
{
    std::cout << "runYolov8" << std::endl;
    cv::Mat modelInput = input;
    if (letterBoxForSquare && modelShape.width == modelShape.height)
        modelInput = formatToSquare(modelInput); // 1280*1280
    std::cout << "letterbox end" << std::endl;

    cv::Mat blob;
    cv::dnn::blobFromImage(modelInput, blob, 1.0 / 255.0, modelShape, cv::Scalar(), true, false);

    std::cout << "blobFromImage end" << std::endl;

    net.setInput(blob);
    std::vector<cv::Mat> outputs;
    net.forward(outputs, net.getUnconnectedOutLayersNames());
    std::cout << "forward end" << std::endl;

    std::cout << "outputs size :" << outputs[0].size() << std::endl;

    // 3. 获取输出张量（shape: [1, num_classes]）
    cv::Mat &prob = outputs[0];
    prob = prob.reshape(1, 1); // 确保形状是 [1, N]

    // 4. 取 Top-5
    std::vector<std::pair<float, int>> prob_index;
    for (int i = 0; i < prob.cols; i++)
    {
        prob_index.emplace_back(prob.at<float>(0, i), i);
    }

    // 按概率降序排序
    std::sort(prob_index.begin(), prob_index.end(),
              [](const std::pair<float, int> &a, const std::pair<float, int> &b)
              {
                  return a.first > b.first;
              });

    // 5. 打印 Top-5 结果
    std::cout << "Top-5 predictions:\n";
    std::vector<std::pair<std::string, float>> top5;
    for (int i = 0; i < 5 && i < prob_index.size(); i++)
    {
        int label_id = prob_index[i].second;
        float confidence = prob_index[i].first;
        std::string label = (label_id < classes.size()) ? classes[label_id] : std::to_string(label_id);
        top5.emplace_back(label, confidence);
        std::cout << "  Rank-" << i + 1
                  << ": label=" << label_id
                  << " (" << label << ")"
                  << ", prob=" << confidence
                  << "\n";
    }

    return top5;
}

void Yolov8::loadClassesFromFile()
{
    std::ifstream inputFile(classesPath);
    if (inputFile.is_open())
    {
        std::string classLine;
        while (std::getline(inputFile, classLine))
            classes.push_back(classLine);
        inputFile.close();
    }
    std::cout << "load end" << std::endl;
}

void Yolov8::loadOnnxNetwork()
{
    net = cv::dnn::readNetFromONNX(modelPath);
    if (cudaEnabled)
    {
        std::cout << "\nRunning on CUDA" << std::endl;
        net.setPreferableBackend(cv::dnn::DNN_BACKEND_CUDA);
        net.setPreferableTarget(cv::dnn::DNN_TARGET_CUDA);
    }
    else
    {
        std::cout << "\nRunning on CPU" << std::endl;
        net.setPreferableBackend(cv::dnn::DNN_BACKEND_OPENCV);
        net.setPreferableTarget(cv::dnn::DNN_TARGET_CPU);
    }
}

cv::Mat Yolov8::formatToSquare(const cv::Mat &source)
{
    int col = source.cols;
    int row = source.rows;


    int _min = MIN(col, row);
    // 计算中心裁剪区域
    int top = (row - _min) / 2;
    int left = (col - _min) / 2;
    cv::Mat cropped = source(cv::Rect(left, top, _min, _min));
    cv::Mat resized;
    cv::resize(cropped, resized, cv::Size(224, 224), 0, 0, cv::INTER_LINEAR);
    return resized;
}

int main(int argc, char **argv)
{
    // std::string img_path = "../data/test.jpg";
    // std::string img_path = "../data/watermelon_18.jpg";
    // std::string img_path = "../data/broccoli_112.jpg";
    // std::string img_path = "/home/<USER>/Dukto/23bxw82wv7ms2yf0gydrbkd7n.jpg";
    std::string img_path = "../data/food_classify/tomato/5trjvtp9t20xol90d7hirnb2t.jpg";

    // 模型路径
    std::string classify_model_path = "../models/food.onnx";
    std::string classify_label_path = "../models/food.txt";
    Yolov8 yolov8(classify_model_path, classify_label_path);

    std::string root_path = "/home/<USER>/下载/图片数据（算法用）(1)/2024年/安品云图片数据2024";
    std::string save_root = "../data/food_classify";
    int img_count = 0;

    // cv::Mat img = cv::imread(img_path);
    //             std::vector<std::pair<std::string, float>> results;
    //         results = yolov8.runYolov8(img);

    for (const auto &entry : std::filesystem::recursive_directory_iterator(root_path))
    {
        if (entry.is_regular_file())
        {
            std::string img_path = entry.path().string();
            cv::Mat img = cv::imread(img_path);
            if (img.empty())
            {
                std::cout << "无法读取图片: " << img_path << std::endl;
                continue;
            }
            std::cout << "\n推理图片: " << img_path << std::endl;
            std::vector<std::pair<std::string, float>> results;
            results = yolov8.runYolov8(img);

            if (!results.empty() && results[0].second > 0.8)
            {
                std::string label = results[0].first;
                std::string save_dir = save_root + "/" + label;
                std::filesystem::create_directories(save_dir);
                std::string filename = entry.path().filename().string();
                std::string save_path = save_dir + "/" + filename;
                try
                {
                    std::filesystem::copy_file(img_path, save_path, std::filesystem::copy_options::overwrite_existing);
                    std::cout << "已保存到: " << save_path << std::endl;
                }
                catch (std::exception &e)
                {
                    std::cout << "保存失败: " << e.what() << std::endl;
                }
            }

            img_count++;
        }
    }

    return 0;
}