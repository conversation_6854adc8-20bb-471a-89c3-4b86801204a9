CMAKE_MINIMUM_REQUIRED(VERSION 3.0.0)
project(yolov8)


#onnxruntime
# set(ONNXRUNTIME_ROOT_PATH /data/DataBackup/deeptools/onnxruntime-linux-x64-1.14.1)
# set(ONNXRUNTIME_INCLUDE_DIRS ${ONNXRUNTIME_ROOT_PATH}/include)
# set(OnnxRuntime_LIBS ${ONNXRUNTIME_ROOT_PATH}/lib/libonnxruntime.so)
# include_directories(${ONNXRUNTIME_INCLUDE_DIRS})


#opencv
set(CMAKE_PREFIX_PATH /home/<USER>/Environments/opencv-4.8.0/release)
find_package(OpenCV REQUIRED)
include_directories(${OpenCV_INCLUDE_DIRS})
 
SET(CMAKE_CXX_STANDARD 17)
SET(CMAKE_CXX_STANDARD_REQUIRED ON)

# SET (ONNXRUNTIME_DIR /data/DataBackup/deeptools/onnxruntime-linux-x64-1.14.1)

FIND_PACKAGE(OpenCV REQUIRED)
#include_directories("")
ADD_EXECUTABLE(classify test_classify.cpp yolov8_utils.cpp yolov8_utils.h)

# TARGET_INCLUDE_DIRECTORIES(yolov8 PRIVATE "${ONNXRUNTIME_DIR}/include")

TARGET_COMPILE_FEATURES(classify PRIVATE cxx_std_14)
TARGET_LINK_LIBRARIES(classify ${OpenCV_LIBS})



ADD_EXECUTABLE(segment test_seg.cpp yolov8_utils.cpp yolov8_utils.h)

# TARGET_INCLUDE_DIRECTORIES(yolov8 PRIVATE "${ONNXRUNTIME_DIR}/include")

TARGET_COMPILE_FEATURES(segment PRIVATE cxx_std_14)
TARGET_LINK_LIBRARIES(segment ${OpenCV_LIBS})
# if (WIN32)
    # TARGET_LINK_LIBRARIES(yolov8 "${ONNXRUNTIME_DIR}/lib/onnxruntime.lib")
# endif(WIN32)
# 
# if (UNIX)
    # TARGET_LINK_LIBRARIES(yolov8 "${ONNXRUNTIME_DIR}/lib/libonnxruntime.so")
# endif(UNIX)